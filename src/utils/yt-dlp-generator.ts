import type { IVideo } from '../interfaces/video';
import type { IVideoClip } from '../interfaces/clip-time';
import { secondToTimeString } from './second-to-time-string';

export interface CommandOptions {
	includeMetadata?: boolean;
	includeSubtitles?: boolean;
	writeAutoSubs?: boolean;
	outputTemplate?: string;
	delogoRegions?: IDelogoRegion[];
	manualBrowser?: string | null;
	videoHeight?: number; // 用于限制下载分辨率，确保与delogo区域匹配
}

export interface IDelogoRegion {
	id: string;
	name: string;
	x: number;
	y: number;
	width: number;
	height: number;
	enabled: boolean;
}

/**
 * 生成单个片段的yt-dlp命令
 */
export function generateSingleClipCommand(
	videoId: string,
	clip: IVideoClip,
	options: CommandOptions = {}
): string {
	const {
		includeMetadata = false,
		includeSubtitles = false,
		writeAutoSubs = false,
		outputTemplate,
		delogoRegions = [],
		manualBrowser = null,
		videoHeight
	} = options;

	// 优先使用片段级别的delogo区域，如果没有则使用传入的delogoRegions
	const effectiveDelogoRegions = clip.delogoRegions || delogoRegions;

	const videoUrl = `https://www.youtube.com/watch?v=${videoId}`;
	const startTime = secondToTimeString(clip.start);
	const endTime = secondToTimeString(clip.end);
	const duration = Math.round(clip.end - clip.start);

	// 生成文件名
	const clipName = clip.name || `clip_${startTime.replace(/:/g, '-')}`;
	const currentDate = new Date().toISOString().slice(0, 10).replace(/-/g, '');

	const commandArgs: string[] = [`yt-dlp`, `"${videoUrl}"`];

	// 1. 添加浏览器cookie参数（自动检测或手动选择）
	const cookieParam = manualBrowser ? `--cookies-from-browser ${manualBrowser}` : getYtDlpCookieParam();
	if (cookieParam) {
		commandArgs.push(cookieParam);
	}

	// 2. 添加分辨率限制（完全依赖格式排序）
	if (videoHeight && videoHeight > 0) {
		// 使用格式排序，优先选择VP9编码和检测到的分辨率
		commandArgs.push(`-f`, `"bestvideo+bestaudio"`);
		commandArgs.push(`-S`, `"vcodec:vp9,height:${videoHeight}"`);
	} else {
		// 没有检测到分辨率时，也使用格式排序但用默认720p
		commandArgs.push(`-f`, `"bestvideo+bestaudio"`);
		commandArgs.push(`-S`, `"vcodec:vp9,height:720"`);
	}

	// 3. 添加片段下载参数
	commandArgs.push(`--download-sections`, `"*${startTime}-${endTime}"`);

	// 4. 添加视频重编码参数
	commandArgs.push(`--recode-video`, `mp4`);

	// 5. 添加FFmpeg后处理参数（delogo滤镜）
	if (effectiveDelogoRegions && effectiveDelogoRegions.length > 0) {
		const enabledRegions = effectiveDelogoRegions.filter((region: IDelogoRegion) => region.enabled);
		if (enabledRegions.length > 0) {
			const delogoFilter = generateDelogoFilter(enabledRegions);
			if (delogoFilter) {
				commandArgs.push(`--ppa`, `"ffmpeg:-vf ${delogoFilter}"`);
			}
		}
	}

	// 6. 添加可选参数
	if (includeMetadata) {
		commandArgs.push(`--embed-metadata`);
		commandArgs.push(`--ignore-errors`);
	}
	if (includeSubtitles) {
		if (writeAutoSubs) {
			commandArgs.push(`--write-auto-subs`);
		} else {
			commandArgs.push(`--embed-subs`);
		}
	}

	// 7. 添加默认下载路径
	commandArgs.push(`-P`, `"~/Downloads"`);

	// 8. 添加输出模板
	const template = outputTemplate || `${clipName}_${duration}s_${currentDate}_%(id)s_%(uploader)s.%(ext)s`;
	commandArgs.push(`--output`, `"${template}"`);

	return commandArgs.join(' ');
}

/**
 * 生成delogo滤镜参数
 */
function generateDelogoFilter(regions: IDelogoRegion[]): string {
	if (!regions || regions.length === 0) {
		return '';
	}

	// 生成多个delogo滤镜，用逗号连接
	const delogoFilters = regions.map(region => {
		const { x, y, width, height } = region;
		return `delogo=x=${x}:y=${y}:w=${width}:h=${height}`;
	});

	return delogoFilters.join(',');
}

/**
 * 自动检测浏览器并生成cookie参数
 */
function getYtDlpCookieParam(): string {
	// 简化版本，实际实现需要检测浏览器
	return '--cookies-from-browser chrome';
}

/**
 * 获取浏览器检测信息
 */
export function getBrowserDetectionInfo() {
	return {
		browser: 'Chrome',
		isSupported: true
	};
}

/**
 * 解析和验证yt-dlp命令
 */
export function validateCommand(command: string): {
	isValid: boolean;
	errors: string[];
	warnings: string[];
} {
	const errors: string[] = [];
	const warnings: string[] = [];

	// 基础验证
	if (!command.includes('yt-dlp')) {
		errors.push('命令必须包含 yt-dlp');
	}

	if (!command.includes('youtube.com/watch')) {
		errors.push('必须包含有效的YouTube视频URL');
	}

	if (!command.includes('--download-sections')) {
		warnings.push('未指定下载片段，将下载整个视频');
	}

	if (!command.includes('--output')) {
		warnings.push('未指定输出文件名模板');
	}

	return {
		isValid: errors.length === 0,
		errors,
		warnings
	};
}